<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <title>Tutors Alliance Scotland - Dynamic Page</title>
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    <!-- CSS LOAD ORDER (specificity increases with load order):
         1. styles2.css - Base styles, layout containers, main components
         2. footer-module.css - Footer-specific styles
         3. button-module.css - Button components and variants
         4. typography-module.css - Font styles, text formatting
         5. animation-module.css - Animations, transitions, transforms
         6. header-banner.css - Header and rolling banner styles
         7. nav.css - Navigation styles (HIGHEST SPECIFICITY - loads last)
    -->
    <link rel="stylesheet" href="/css/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/css/animation-module.css">
    <link rel="stylesheet" href="/css/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/js/responsive-helper.js" defer=""></script>
    <script src="/js/nav-loader.js" defer=""></script>
    <script src="/js/dynamic-nav.js" defer=""></script>
    <script src="/js/rolling-banner.js" defer=""></script>
    <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747300" defer=""></script>
</head>
<body class="dynamic-page" data-page="page">
    <header>
        <h1 data-ve-block-id="47a1e31f-beac-46e8-86bf-e0ad03809e7a">Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a href="/" class="banner-login-link login-box" data-ve-block-id="b921641e-4859-4839-9c8e-d8b9f22ecade">Home</a>
            <a href="/login.html?role=admin" class="banner-login-link login-box" data-ve-block-id="669c2938-dfd4-4e77-ba81-453bb16810fd">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <!-- Rolling banner container -->
    <div class="rolling-banner">
        <div class="rolling-content" id="tutorBanner">
            <!-- JS or server code can populate tutor names/subjects here -->
        </div>
    </div>

    <main>
        <div class="mission-row">
            <!-- LEFT COLUMN: Shield + Ribbons -->
            <div class="left-col">
                <img src="/images/centralShield.png" alt="Large STA Shield" class="main-shield" id="imageShield" data-ve-block-id="ea77519d-94b2-41cb-a754-60772c82a626">
                <img src="/images/bannerWithRibbons.png" alt="Banner Ribbon" class="main-ribbons" id="imageBanner" data-ve-block-id="db996137-cc99-4cee-954c-42ecc99d2e8c">
            </div>

            <!-- RIGHT COLUMN: Dynamic page content -->
            <div class="right-col" id="pageContent">
                <!-- Dynamic page content will be inserted here -->
                <div class="loading-indicator">Loading page content now...</div>
            </div>
        </div>

        <!-- Clear separator before dynamic sections -->
        <div class="dynamic-sections-separator"></div>

        <!-- Dynamic sections will be added here -->
        <section id="dynamicSections"></section>
    </main>

    <!-- SOCIAL ICONS FOOTER -->
    <footer class="site-footer fade-in-section">
        <div class="footer-icons">
            <a href="#" aria-label="Facebook" data-ve-block-id="07339341-5ece-4d8d-ae1e-97b363e3f852"><i class="fab fa-facebook-f"></i></a>
            <a href="#" aria-label="Instagram" data-ve-block-id="23a5117d-77a3-46fb-a656-e67e24d8c0a3"><i class="fab fa-instagram"></i></a>
        </div>
    </footer>

    <!-- STATIC BOTTOM FOOTER -->
    <footer class="static-footer">
        <div class="static-footer-container">
            <div class="static-footer-left">
                <h4 data-ve-block-id="6616bb5c-d631-4820-92b8-9379fd28b25d">Extra Information</h4>
                <ul>
                    <li><a href="tutoring-standards.html" data-ve-block-id="5f8c25c0-d84d-4b97-8530-ecf6cff5a843">Our Tutoring Standards</a></li>
                    <li><a href="faq.html" data-ve-block-id="3420da20-28d0-4620-a226-a5d09c9bd120">FAQ's</a></li>
                    <li><a href="privacy-policy.html" data-ve-block-id="************************************">Privacy Policy</a></li>
                    <li><a href="safeguarding-policy.html" data-ve-block-id="217b1f04-73f0-49c5-9937-aed8e3cc9319">Safeguarding Policy</a></li>
                    <li><a href="terms-and-conditions.html" data-ve-block-id="03d45ef5-435a-43ca-8975-37dfe3ee1405">Terms and Conditions</a></li>
                </ul>
                <div class="static-footer-copyright">
                    <p data-ve-block-id="c292e755-569d-4016-b724-564a1acac34e">ALL RIGHTS RESERVED © Tutors Alliance Scotland 2023</p>
                    <p data-ve-block-id="89fe3b01-6f3f-43b8-8f82-5bb5a0c97a83">Tutors Alliance Scotland is VAT registered</p>
                    <p data-ve-block-id="1b6390d2-084c-43de-b850-5c357e55f4bc">VAT No 429 8003 10</p>
                </div>
                <div class="static-footer-credits">
                    <p data-ve-block-id="8d09c0c8-7134-4a51-8381-ecc934335a02">Website by <a href="#" target="_blank" data-ve-block-id="94cb6746-2b07-4010-8ae4-227a22a8318b">Tutors Alliance Scotland</a></p>
                </div>
            </div>
            <div class="static-footer-right">
                <div class="business-insurance">
                    <img src="/images/centralShield.png" alt="Business Insurance" class="insurance-logo" data-ve-block-id="3c0e3d93-a63d-4432-ba3d-354bd3a92132">
                    <p data-ve-block-id="9632da2a-51b4-4e1a-94d2-4d25f80c2384">Business Insurance provided through Tutors Alliance Scotland</p>
                    <a href="#" class="insurance-link" data-ve-block-id="d7c904ba-b1dc-46b8-8f3c-836b8242d51b">View our insurance details</a>
                </div>
                <div class="website-url">
                    <p data-ve-block-id="fd15afb2-1813-4221-b858-47280e59afec">www.tutorsalliancescotland.org.uk</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <script>
        function goToLogin(role) {
            window.location.href = `/login.html?role=${encodeURIComponent(role)}`;
        }

        // Rolling banner is now handled by rolling-banner.js

        window.addEventListener('DOMContentLoaded', () => {
            // After the heading's animation delay (say 1.5s) plus a little buffer:
            setTimeout(() => {
                document.querySelectorAll('.fade-later').forEach(el => {
                    el.classList.add('fade-in');
                });
            }, 1500); // or 2000 if you want a bit more buffer
        });
    </script>

    <script>
        // Single Intersection Observer for all fade-in elements
        const fadeObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('is-visible');
                    fadeObserver.unobserve(entry.target);
                }
            });
        }, {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        });

        // Function to observe all fade-in elements
        function observeFadeElements() {
            // Observe all sections with fade-in-section class
            document.querySelectorAll('.fade-in-section').forEach(section => {
                fadeObserver.observe(section);
            });
        }

        // Initial observation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            observeFadeElements();
        });

        // Watch for dynamically added content
        const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length) {
                    observeFadeElements();
                }
            });
        });

        // Start observing the document body for changes
        mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>

    <script type="module">
        // Get the page slug from the URL
        const pathParts = location.pathname.split('/');
        const slug = pathParts[pathParts.length - 1];

        console.log('Loading page with slug:', slug);

        // Fetch the page content
        fetch(`/api/page?slug=${slug}`)
            .then(r => {
                if (!r.ok) {
                    console.error('API response not OK:', r.status, r.statusText);
                    // Try to get the error message from the response
                    return r.text().then(errorText => {
                        console.error('API error details:', errorText);
                        throw new Error(`Page not found: ${r.status} - ${errorText}`);
                    });
                }
                return r.json();
            })
            .then(page => {
                console.log('Page data received:', page);
                document.title = `${page.heading} - Tutors Alliance Scotland`;

                // Build the HTML content using the two-col-content structure like other pages
                let contentHTML = '';

                // Determine if we have an image for layout purposes
                const hasImage = page.image && page.image.trim() !== '';
                const sectionClass = hasImage ? 'two-col-content fade-in-section' : 'two-col-content single-column fade-in-section';

                // Create the white content section with curved corners
                contentHTML += `<section class="${sectionClass}">`;

                // Left column: text content
                contentHTML += `<div>`;
                contentHTML += `<h2>${page.heading}</h2>`;
                contentHTML += `<div class="page-content">${page.text}</div>`;

                // Add button if provided
                if (page.buttonLabel && page.buttonUrl) {
                    contentHTML += `<div style="text-align:center;margin:2rem 0;">
                                      <a class="button aurora" href="${page.buttonUrl}">${page.buttonLabel}</a>
                                    </div>`;
                }
                contentHTML += `</div>`;

                // Right column: image (only if provided)
                if (hasImage) {
                    contentHTML += `<div>`;
                    contentHTML += `<img src="${page.image}" alt="${page.heading}">`;
                    contentHTML += `</div>`;
                }

                contentHTML += `</section>`;

                document.getElementById('pageContent').innerHTML = contentHTML;

                // Re-observe fade-in elements after content is loaded
                observeFadeElements();
            })
            .catch(error => {
                console.error('Error loading page:', error);
                const errorHTML = `
                    <section class="two-col-content fade-in-section">
                        <div>
                            <h2>Page Not Found</h2>
                            <p>Sorry, the page you requested could not be found.</p>
                            <p><strong>Debug info:</strong> ${error.message}</p>
                            <p><strong>Slug:</strong> ${slug}</p>
                            <p><strong>URL:</strong> ${location.pathname}</p>
                            <p><a href="/" class="button aurora">Return to Home</a></p>
                        </div>
                    </section>
                `;
                document.getElementById('pageContent').innerHTML = errorHTML;

                // Re-observe fade-in elements after error content is loaded
                observeFadeElements();
            });

        // ✅ NEW: Add UUID generator utility function (same as dynamic-sections.js)
        function uuidv4() {
            return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
                (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
            );
        }

        // ✅ NEW: Add helper to inject IDs into HTML string (same as dynamic-sections.js)
        function ensureBlockIds(htmlString) {
            if (!htmlString) return '';
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlString;
            const editableTags = ['p', 'img', 'a', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
            editableTags.forEach(tag => {
                tempDiv.querySelectorAll(tag).forEach(el => {
                    if (!el.hasAttribute('data-ve-block-id')) {
                        el.setAttribute('data-ve-block-id', uuidv4());
                    }
                });
            });
            return tempDiv.innerHTML;
        }

        // Load any additional dynamic sections for this page
        fetch(`/api/sections?page=${slug}`)
            .then(r => r.json())
            .then(list => {
                console.log('Dynamic sections for page:', list);
                if (list && list.length > 0) {
                    // Only show the dynamic sections container if there are sections to display
                    const host = document.getElementById('dynamicSections');

                    // Clear any existing content
                    host.innerHTML = '';

                    // Add each section with proper spacing and styling
                    list.forEach((s, index) => {
                        // ✅ FIXED: Create section with proper block IDs
                        const article = document.createElement('article');
                        article.className = 'dyn-block fade-in-section';
                        article.style.transitionDelay = `${index * 0.1}s`;
                        article.dataset.veSectionId = s._id || s.heading.toLowerCase().replace(/\s+/g, '-');

                        // Add image with stable block ID from database
                        if (s.image) {
                            const imageBlockId = s.imageBlockId || uuidv4();
                            article.insertAdjacentHTML('beforeend',
                                `<img src="${s.image}" alt="${s.heading}" loading="lazy" data-ve-block-id="${imageBlockId}">`
                            );
                        }

                        // Add heading with stable block ID from database
                        const headingBlockId = s.headingBlockId || uuidv4();
                        const heading = document.createElement('h2');
                        heading.textContent = s.heading;
                        heading.setAttribute('data-ve-block-id', headingBlockId);
                        article.appendChild(heading);

                        // Add content with stable block ID from database and ensure nested IDs
                        const contentBlockId = s.contentBlockId || uuidv4();
                        const content = document.createElement('div');
                        content.className = 'dyn-content';
                        content.innerHTML = ensureBlockIds(s.text);
                        content.setAttribute('data-ve-block-id', contentBlockId);
                        article.appendChild(content);

                        host.appendChild(article);
                    });

                    // Make sure the dynamic sections container is visible
                    host.style.display = 'block';

                    // Ensure the separator is visible
                    document.querySelector('.dynamic-sections-separator').style.display = 'block';
                } else {
                    // Hide containers if no sections
                    const host = document.getElementById('dynamicSections');
                    if (host) host.style.display = 'none';
                    const separator = document.querySelector('.dynamic-sections-separator');
                    if (separator) separator.style.display = 'none';
                }

                // ✅ NEW: Dispatch event to notify visual editor that dynamic content is ready
                window.dispatchEvent(new CustomEvent('dyn-sections-loaded'));
                document.body.classList.add('dyn-ready');
                console.log('[Page] Dispatched "dyn-sections-loaded" event.');
            })
            .catch(error => {
                console.error('Error loading dynamic sections for page:', error);
                // Hide containers on error
                const host = document.getElementById('dynamicSections');
                if (host) host.style.display = 'none';
                const separator = document.querySelector('.dynamic-sections-separator');
                if (separator) separator.style.display = 'none';

                // ✅ NEW: Still dispatch event to unblock visual editor
                window.dispatchEvent(new CustomEvent('dyn-sections-loaded'));
                document.body.classList.add('dyn-ready');
                console.log('[Page] Dispatched "dyn-sections-loaded" event after error.');
            })
            .catch(error => {
                console.error('Error loading dynamic sections:', error);
                // Hide the container and separator on error
                document.getElementById('dynamicSections').style.display = 'none';
                document.querySelector('.dynamic-sections-separator').style.display = 'none';
            });
    </script>

    <!-- order matters -- all scripts are defered so they execute in DOM order -->

    <script src="/js/responsive-helper.js" defer=""></script>   <!-- defines initRollingBanner -->
    <script src="/js/rolling-banner.js" defer=""></script>   <!-- *uses* initRollingBanner -->
    <script src="/js/dynamic-nav.js" defer=""></script>
    <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747300" defer=""></script>

    <!-- bump the version whenever you redeploy -->
    <script src="/js/dynamic-sections.js?v=20240530" type="module" defer=""></script>
<!-- Visual Editor Templates -->
<template id="ve-editor-modal-template">
    <div id="editor-modal" class="ve-modal-container">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Edit Content</h3>
                <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
            </div>
            <div class="modal-body">
                <form id="content-form" onsubmit="return false;">
                    <div class="form-group">
                        <label for="content-type">Content Type:</label>
                        <select id="content-type" class="form-control" disabled="">
                            <option value="text">Text</option>
                            <option value="html">HTML</option>
                            <option value="image">Image</option>
                            <option value="link">Link</option>
                        </select>
                    </div>
                    <div class="form-group" id="text-group">
                        <label for="content-text">Text Content:</label>
                        <textarea id="content-text" class="form-control" rows="8" placeholder="Enter your text content here..."></textarea>
                        
                        <!-- Button Management for Text Elements -->
                        <div class="text-button-management">
                            <h4>Add Button</h4>
                            <p class="help-text">Add an aurora-style button at the end of this text element</p>
                            
                            <div id="text-buttons-list" class="text-buttons-list">
                                <!-- Existing buttons will be listed here -->
                            </div>
                            
                            <div class="button-form" id="new-button-form" style="display: none;">
                                <div class="form-group">
                                    <label for="new-button-text">Button Text:</label>
                                    <input type="text" id="new-button-text" class="form-control" placeholder="Enter button text">
                                </div>
                                <div class="form-group">
                                    <label for="new-button-url">Button URL:</label>
                                    <input type="url" id="new-button-url" class="form-control" placeholder="https://example.com">
                                </div>
                                <div class="button-form-actions">
                                    <button type="button" id="save-new-button" class="btn btn-primary">Add Button</button>
                                    <button type="button" id="cancel-new-button" class="btn btn-secondary">Cancel</button>
                                </div>
                            </div>
                            
                            <button type="button" id="add-text-button" class="btn btn-secondary">+ Add Button</button>
                        </div>
                    </div>
                    <div class="form-group" id="html-group">
                        <label for="content-html">HTML Content:</label>
                        <textarea id="content-html" class="form-control" rows="10" placeholder="Enter your HTML content here..."></textarea>
                    </div>
                    <div class="form-group" id="image-group">
                        <label for="content-image">Image URL:</label>
                        <div class="image-input-group">
                            <input type="url" id="content-image" class="form-control" placeholder="Enter image URL or browse/upload below">
                            <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                        </div>
                        <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                        <div class="upload-section">
                            <label for="image-upload">Or upload a new image:</label>
                            <input type="file" id="image-upload" accept="image/*" class="form-control">
                            <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                            <div id="upload-progress" style="display: none;">
                                <div class="progress-bar"><div class="progress-fill"></div></div>
                                <span class="progress-text">Uploading...</span>
                            </div>
                        </div>
                        <label for="image-alt">Alt Text:</label>
                        <input type="text" id="image-alt" class="form-control" placeholder="Describe the image for accessibility">
                    </div>
                    <div class="form-group" id="link-group">
                        <label for="link-url">Link URL:</label>
                        <input type="url" id="link-url" class="form-control" placeholder="https://example.com">
                        <label for="link-text">Link Text:</label>
                        <input type="text" id="link-text" class="form-control" placeholder="Enter the text to display">
                        <div class="form-check">
                            <input type="checkbox" id="link-is-button" class="form-check-input">
                            <label for="link-is-button" class="form-check-label">Style as button</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                        <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                        <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                    </div>
                </form>
            </div>
        </div>
        <div id="image-browser" class="image-browser" style="display: none;"></div>
    </div>
</template>

<template id="ve-image-browser-template">
    <div class="image-browser-header">
        <h4>Browse Images</h4>
        <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
    </div>
    <div class="image-browser-content">
        <div class="image-browser-toolbar">
            <input type="text" id="image-search" placeholder="Search images..." class="form-control">
            <select id="image-sort" class="form-control">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name</option>
            </select>
        </div>
        <div id="image-grid" class="image-grid">
            <div class="loading-spinner"></div>
        </div>
        <div id="image-pagination" class="image-pagination">
            <button type="button" id="prev-page" class="btn btn-secondary" disabled="">Previous</button>
            <span id="page-info">Page 1</span>
            <button type="button" id="next-page" class="btn btn-secondary">Next</button>
        </div>
    </div>
</template>


</body></html>