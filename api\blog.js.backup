// BACKUP of original blog.js - Combined into content-display.js
// This file is kept as backup for rollback purposes
// Original functionality moved to content-display.js

// api/blog.js
const connectToDatabase = require('./connectToDatabase');
const Blog = require('../models/Blog');

module.exports = async (req, res) => {
    try {
        await connectToDatabase();

        // Grab ?category= from the query string (e.g. /blog?category=secondary)
        const { category } = req.query;

        // Build a query object:
        let query = {};

        if (category && category !== 'all') {
            if (category === 'general') {
                // "General" means posts that have BOTH "parent" and "tutor" in the category array.
                query.category = { $all: ['parent', 'tutor'] };
            } else {
                // Otherwise, filter by the specific category.
                query.category = category;
            }
        }

        // Fetch posts from DB with the optional filter, sorted by newest first
        const posts = await Blog.find(query).sort({ createdAt: -1 });

        // Group posts into pairs for the grid layout
        const postPairs = [];
        for (let i = 0; i < posts.length; i += 2) {
            if (i + 1 < posts.length) {
                // If we have a pair
                postPairs.push([posts[i], posts[i + 1]]);
            } else {
                // If we have an odd number of posts, the last one goes alone
                postPairs.push([posts[i]]);
            }
        }

        // Select a hero image from available images
        const heroImages = [
            '/images/parentAndChild.PNG',
            '/images/childStudy.PNG',
            '/images/tutorStatic1.PNG',
            '/images/tutorStatic2.PNG',
            '/images/tutorStatic3.PNG',
            '/images/tutorStatic4.PNG'
        ];
        const heroImage = heroImages[Math.floor(Math.random() * heroImages.length)];

        // Generate HTML for the blog grid with hero section
        const postsHtml = `
        <!-- Hero Banner -->
        <section class="blog-hero-banner fade-in-section">
            <div class="blog-hero-overlay">
                <h1>Tutors Alliance Scotland Blog</h1>
                <p>Insights, tips and news for tutors, parents and students</p>
                <div class="hero-filter-buttons">
                    <button type="button" class="category-filter-btn category-all ${!category ? 'active' : ''}" data-category="">All Posts</button>
                    <button type="button" class="category-filter-btn category-general ${category === 'general' ? 'active' : ''}" data-category="general">General</button>
                    <button type="button" class="category-filter-btn category-parent ${category === 'parent' ? 'active' : ''}" data-category="parent">Parent</button>
                    <button type="button" class="category-filter-btn category-tutor ${category === 'tutor' ? 'active' : ''}" data-category="tutor">Tutor</button>
                </div>
            </div>
        </section>

        <!-- Blog Grid Layout -->
        <div class="blog-grid-container">
            ${postPairs.map((pair, pairIndex) => `
            <!-- Blog Row ${pairIndex + 1} -->
            <div class="blog-row fade-in-section">
                ${pair.map((post, postIndex) => {
                    // Alternate styles between posts
                    const styleClass = (pairIndex + postIndex) % 2 === 0 ? 'parent-gradient-bg' : 'tutor-gradient-bg';
                    const boxClass = (pairIndex + postIndex) % 2 === 0 ? 'parent-box' : 'tutor-box';

                    // Get category display name
                    let categoryDisplay = '';
                    let categoryClass = '';

                    if (post.category && post.category.length > 0) {
                        if (post.category.includes('parent') && post.category.includes('tutor')) {
                            categoryDisplay = 'General';
                            categoryClass = 'category-general';
                        } else if (post.category.includes('parent')) {
                            categoryDisplay = 'Parent';
                            categoryClass = 'category-parent';
                        } else if (post.category.includes('tutor')) {
                            categoryDisplay = 'Tutor';
                            categoryClass = 'category-tutor';
                        }
                    }

                    return `
                <div class="blog-card">
                    <div class="blog-card-inner">
                        ${post.imagePath ?
                            `<div class="blog-image-container">
                                <img src="${post.imagePath}" alt="${post.title}" class="blog-card-image">
                                ${categoryDisplay ? `<span class="blog-category ${categoryClass}">${categoryDisplay}</span>` : ''}
                            </div>` :
                            `${categoryDisplay ? `<span class="blog-category ${categoryClass} no-image">${categoryDisplay}</span>` : ''}`
                        }
                        <div class="blog-card-content">
                            <h2 class="blog-card-title">${post.title}</h2>
                            <p class="blog-card-byline">By ${post.author}</p>
                            ${post.excerpt ?
                                `<p class="blog-card-excerpt">${post.excerpt}</p>` :
                                `<p class="blog-card-excerpt">${post.content.substring(0, 150)}${post.content.length > 150 ? '...' : ''}</p>`}
                            <a href="/blog/${post._id}" class="blog-read-more">Read more</a>
                        </div>
                    </div>
                </div>`;
                }).join('')}
            </div>`).join('')}
        </div>

        <!-- Individual Blog Posts (hidden by default, shown when clicked) -->
        ${posts.map((post, index) => {
            // Get category display name for full post
            let categoryDisplay = '';
            let categoryClass = '';

            if (post.category && post.category.length > 0) {
                if (post.category.includes('parent') && post.category.includes('tutor')) {
                    categoryDisplay = 'General';
                    categoryClass = 'category-general';
                } else if (post.category.includes('parent')) {
                    categoryDisplay = 'Parent';
                    categoryClass = 'category-parent';
                } else if (post.category.includes('tutor')) {
                    categoryDisplay = 'Tutor';
                    categoryClass = 'category-tutor';
                }
            }

            return `
            <!-- Full Blog Post ${index + 1} -->
            <section id="post-${post._id}" class="blog-full-post fade-in-section" style="display: none;">
                <div class="blog-post-hero" style="background-image: url('${post.imagePath || heroImage}')">
                    <div class="blog-post-hero-overlay">
                        ${categoryDisplay ? `<span class="blog-category ${categoryClass}">${categoryDisplay}</span>` : ''}
                    </div>
                </div>
                <div class="blog-post-content">
                    <a href="/blog" class="blog-back-button">&larr; Back to all posts</a>
                    <div class="blog-post-header">
                        <h1>${post.title}</h1>
                        <p class="blog-post-author">By ${post.author}</p>
                    </div>
                    <div class="blog-post-text">
                        ${post.content}
                    </div>
                </div>
            </section>
            `;
        }).join('\n\n        <!-- Add padding between sections -->\n        <div style="margin: 2rem 0;"></div>\n\n')}
        `;

        // Create the full HTML page with the filter form
        const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <link rel="icon" href="/images/bannerShield2.png" type="image/png">
          <title>Tutors Alliance Scotland Blog</title>
          <link rel="stylesheet" href="/css/styles2.css">
          <link rel="stylesheet" href="/css/footer-module.css">
          <link rel="stylesheet" href="/css/button-module.css">
          <link rel="stylesheet" href="/css/typography-module.css">
          <link rel="stylesheet" href="/css/animation-module.css">
          <link rel="stylesheet" href="/css/header-banner.css">
          <link rel="stylesheet" href="/css/nav.css">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <script src="/responsive-helper.js"></script>
          <script src="/js/nav-loader.js" defer></script>
          <script src="/js/dynamic-nav.js" defer></script>
          <style>
            /* Blog Hero Banner */
            .blog-hero-banner {
              position: relative;
              width: 100%;
              height: 400px;
              background: linear-gradient(rgba(0, 27, 68, 0.7), rgba(0, 87, 183, 0.7)),
                          url('${heroImage}') center/cover no-repeat;
              margin-bottom: 3rem;
              display: flex;
              align-items: center;
              justify-content: center;
              text-align: center;
              border-radius: 0;
            }

            .blog-hero-overlay {
              padding: 2rem;
              color: white;
              max-width: 800px;
              z-index: 2;
            }

            .blog-hero-overlay h1 {
              font-size: 3.5rem;
              margin-bottom: 1rem;
              text-shadow: 0 2px 8px rgba(0,0,0,0.5);
              color: white;
              font-weight: 700;
            }

            .blog-hero-overlay p {
              font-size: 1.5rem;
              opacity: 0.9;
              text-shadow: 0 1px 2px rgba(0,0,0,0.3);
              margin-bottom: 1.5rem;
            }

            .hero-filter-buttons {
              display: flex;
              gap: 1rem;
              justify-content: center;
              flex-wrap: wrap;
              margin-top: 1.5rem;
            }

            .blog-hero-banner .category-filter-btn {
              background-color: rgba(255, 255, 255, 0.2);
              backdrop-filter: blur(5px);
              border: 1px solid rgba(255, 255, 255, 0.3);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }

            .blog-hero-banner .category-filter-btn.active {
              background-color: rgba(255, 255, 255, 0.3);
              border: 1px solid rgba(255, 255, 255, 0.5);
              box-shadow: 0 6px 16px rgba(0, 0, 0, 0.25);
            }

            /* Blog Grid Layout Styles */
            .blog-grid-container {
              display: flex;
              flex-direction: column;
              gap: 2.5rem;
              max-width: 1200px;
              margin: 0 auto 3rem;
              padding: 0 1.5rem;
            }

            .blog-row {
              display: flex;
              flex-wrap: wrap;
              gap: 2.5rem;
              justify-content: center;
            }

            .blog-card {
              flex: 1;
              min-width: 300px;
              max-width: 580px;
              margin-bottom: 1rem;
              border-radius: 0.75rem;
              overflow: hidden;
              box-shadow: 0 6px 18px rgba(0, 57, 122, 0.12);
              transition: transform 0.3s ease, box-shadow 0.3s ease;
              background-color: #f8f9fa;
            }

            .blog-card:hover {
              transform: translateY(-5px);
              box-shadow: 0 12px 28px rgba(0, 57, 122, 0.18);
            }

            .blog-card-inner {
              height: 100%;
              display: flex;
              flex-direction: column;
              border-radius: 0.75rem;
              overflow: hidden;
              background-color: white;
            }

            .blog-image-container {
              width: 100%;
              height: 280px;
              overflow: hidden;
              position: relative;
            }

            .blog-card-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.5s ease;
            }

            .blog-card:hover .blog-card-image {
              transform: scale(1.05);
            }

            .blog-card-content {
              padding: 1.75rem;
              display: flex;
              flex-direction: column;
              flex-grow: 1;
              background-color: white;
            }

            .blog-card-title {
              font-size: 1.6rem;
              margin-bottom: 0.75rem;
              color: #0057B7;
              line-height: 1.3;
            }

            .blog-card-byline {
              font-size: 0.95rem;
              color: #666;
              margin-bottom: 1.25rem;
            }

            .blog-card-excerpt {
              margin-bottom: 1.75rem;
              flex-grow: 1;
              line-height: 1.7;
              color: #333;
            }

            .blog-read-more {
              align-self: flex-start;
              padding: 0.6rem 1.2rem;
              background-color: #0057B7;
              color: white;
              text-decoration: none;
              border-radius: 0.3rem;
              font-weight: 500;
              transition: all 0.3s ease;
              box-shadow: 0 2px 5px rgba(0, 87, 183, 0.2);
            }

            .blog-read-more:hover {
              background-color: #003A7A;
              box-shadow: 0 4px 8px rgba(0, 87, 183, 0.3);
              transform: translateY(-2px);
            }
          </style>
      </head>
      <body>
          <!-- Shared banner/header -->
          <header>
              <h1>Tutors Alliance Scotland</h1>
              <div class="header-links">
                  <a href="index.html" class="banner-login-link login-box">Home</a>
                  <a href="login.html?role=admin" class="banner-login-link login-box">Login</a>
              </div>
          </header>

          <!-- Navigation will be loaded here by nav-loader.js -->

          <!-- Rolling banner container -->
          <div class="rolling-banner">
              <div class="rolling-content" id="tutorBanner">
                  <!-- JS will populate tutor names/subjects here -->
              </div>
          </div>

          <main>

            <!-- Hidden form for category filtering -->
            <form id="blogFilterForm" style="display: none;">
                <input type="hidden" id="categorySelect" name="category" value="${category || ''}">
            </form>

            <!-- Add padding between sections -->
            <div style="margin: 2rem 0;"></div>

            <!-- Blog posts -->
            ${postsHtml}
          </main>

          <script src="/responsive-helper.js"></script>
          <script>
            // Initialize the rolling banner using responsive-helper.js
            document.addEventListener('DOMContentLoaded', function() {
              initRollingBanner();
            });
          </script>
      </body>
      </html>
    `;

        res.setHeader('Content-Type', 'text/html');
        return res.status(200).send(html);
    } catch (err) {
        console.error('Error in blog route:', err);
        return res.status(500).send('<p>Server Error</p>');
    }
};

// Tell Vercel we need the Node runtime
module.exports.config = { runtime: 'nodejs18.x' };
