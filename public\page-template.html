<!DOCTYPE html><html lang="en"><head>
    <meta charset="UTF-8">
    <title>Tutors Alliance Scotland</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- Favicon -->
    <link rel="icon" href="/images/bannerShield2.png" type="image/png">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/styles2.css">
    <link rel="stylesheet" href="/css/footer-module.css">
    <link rel="stylesheet" href="/css/button-module.css">
    <link rel="stylesheet" href="/css/typography-module.css">
    <link rel="stylesheet" href="/css/animation-module.css">
    <link rel="stylesheet" href="/css/header-banner.css">
    <link rel="stylesheet" href="/css/nav.css">

    <!-- Scripts -->
    <script src="/js/responsive-helper.js" defer=""></script>
    <script src="/js/nav-loader.js" defer=""></script>
    <script src="/js/dynamic-nav.js" defer=""></script>
    <script type="module" src="/js/visual-editor-v2.js?v=20250101-CACHE-BUST&t=1735747300" defer=""></script>
    <script src="/js/rolling-banner.js" defer=""></script>
    <script type="module" src="/js/dynamic-page.js"></script>
</head>
<body>
    <!-- Header/Banner -->
    <header>
        <h1 data-ve-block-id="774db81a-028a-4505-8218-f1b92a05689b">Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a class="banner-login-link login-box" href="/" data-ve-block-id="adb84df3-9710-4be6-b788-4bff808c3fd2">Home</a>
            <a class="banner-login-link login-box" href="/login.html?role=admin" data-ve-block-id="d4878a25-e684-4a2b-9c4d-ec524b63eb3b">Login</a>
        </div>
    </header>

    <!-- Navigation will be loaded here by nav-loader.js -->

    <!-- Main Content -->
    <main>
        <!-- Content will be dynamically inserted here -->
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3 data-ve-block-id="732de1de-8c94-4849-9631-d80a5743db5f">Contact Us</h3>
                <p data-ve-block-id="dd1f685e-1818-48da-a7c9-81b25873b990">Email: <EMAIL></p>
                <p data-ve-block-id="7c7f9562-20a2-446e-aa0d-79a020e44106">Phone: 0131 XXX XXXX</p>
            </div>
            <div class="footer-section">
                <h3 data-ve-block-id="600dbcff-aecf-4374-b41d-55ae847fbad9">Quick Links</h3>
                <ul>
                    <li><a href="/about-us.html" data-ve-block-id="a0c9421c-2763-4b7f-8555-3e0fa73b66ca">About Us</a></li>
                    <li><a href="/tutorMembership.html" data-ve-block-id="195f00a3-69e9-4b5e-b0ab-ac4c3ae1b502">Tutor Membership</a></li>
                    <li><a href="/parents.html" data-ve-block-id="cc7157fd-a2e0-4ed5-b39c-b6bf91bf9845">Parent Zone</a></li>
                    <li><a href="/tutoring-standards.html" data-ve-block-id="b9a6491c-da2e-42b0-b6c4-7ce9c61f7087">The TAS Way: Governance and Guidance</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3 data-ve-block-id="804b87d4-0a4f-486f-a564-7afaef883abc">Follow Us</h3>
                <div class="social-links">
                    <a href="#" target="_blank" data-ve-block-id="da3ca7c9-3377-4fee-9b21-572163dfdf7b">Facebook</a>
                    <a href="#" target="_blank" data-ve-block-id="5dc70a86-9888-4bbc-b662-2e9c73ed1b52">Instagram</a>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p data-ve-block-id="cbd91079-ce16-4aa2-9776-************">© 2025 Tutors Alliance Scotland. All rights reserved.</p>
        </div>
    </footer>
<!-- Visual Editor Templates -->
<template id="ve-editor-modal-template">
    <div id="editor-modal" class="ve-modal-container">
        <div class="modal-backdrop"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Edit Content</h3>
                <button id="close-modal" class="close-btn" aria-label="Close modal">×</button>
            </div>
            <div class="modal-body">
                <form id="content-form" onsubmit="return false;">
                    <div class="form-group">
                        <label for="content-type">Content Type:</label>
                        <select id="content-type" class="form-control" disabled="">
                            <option value="text">Text</option>
                            <option value="html">HTML</option>
                            <option value="image">Image</option>
                            <option value="link">Link</option>
                        </select>
                    </div>
                    <div class="form-group" id="text-group">
                        <label for="content-text">Text Content:</label>
                        <textarea id="content-text" class="form-control" rows="8" placeholder="Enter your text content here..."></textarea>
                        
                        <!-- Button Management for Text Elements -->
                        <div class="text-button-management">
                            <h4>Add Button</h4>
                            <p class="help-text">Add a button at the end of this text element</p>
                            
                            <div id="text-buttons-list" class="text-buttons-list">
                                <!-- Existing buttons will be listed here -->
                            </div>
                            
                            <div class="button-form" id="new-button-form" style="display: none;">
                                <div class="form-group">
                                    <label for="new-button-text">Button Text:</label>
                                    <input type="text" id="new-button-text" class="form-control" placeholder="Enter button text">
                                </div>
                                <div class="form-group">
                                    <label for="new-button-url">Button URL:</label>
                                    <input type="url" id="new-button-url" class="form-control" placeholder="https://example.com">
                                </div>
                                <div class="button-form-actions">
                                    <button type="button" id="save-new-button" class="btn btn-primary">Add Button</button>
                                    <button type="button" id="cancel-new-button" class="btn btn-secondary">Cancel</button>
                                </div>
                            </div>
                            
                            <button type="button" id="add-text-button" class="btn btn-secondary">+ Add Button</button>
                        </div>
                    </div>
                    <div class="form-group" id="html-group">
                        <label for="content-html">HTML Content:</label>
                        <textarea id="content-html" class="form-control" rows="10" placeholder="Enter your HTML content here..."></textarea>
                    </div>
                    <div class="form-group" id="image-group">
                        <label for="content-image">Image URL:</label>
                        <div class="image-input-group">
                            <input type="url" id="content-image" class="form-control" placeholder="Enter image URL or browse/upload below">
                            <button type="button" id="browse-btn" class="btn btn-secondary">Browse Images</button>
                        </div>
                        <div id="image-preview" style="display: none;"><img src="" alt="Preview" style="max-width: 200px; max-height: 200px;"></div>
                        <div class="upload-section">
                            <label for="image-upload">Or upload a new image:</label>
                            <input type="file" id="image-upload" accept="image/*" class="form-control">
                            <button type="button" id="upload-btn" class="btn btn-secondary">Upload Image</button>
                            <div id="upload-progress" style="display: none;">
                                <div class="progress-bar"><div class="progress-fill"></div></div>
                                <span class="progress-text">Uploading...</span>
                            </div>
                        </div>
                        <label for="image-alt">Alt Text:</label>
                        <input type="text" id="image-alt" class="form-control" placeholder="Describe the image for accessibility">
                    </div>
                    <div class="form-group" id="link-group">
                        <label for="link-url">Link URL:</label>
                        <input type="url" id="link-url" class="form-control" placeholder="https://example.com">
                        <label for="link-text">Link Text:</label>
                        <input type="text" id="link-text" class="form-control" placeholder="Enter the text to display">
                        <div class="form-check">
                            <input type="checkbox" id="link-is-button" class="form-check-input">
                            <label for="link-is-button" class="form-check-label">Style as button</label>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="preview-btn" class="btn btn-secondary">Preview</button>
                        <button type="button" id="save-btn" class="btn btn-primary">Save Changes</button>
                        <button type="button" id="restore-btn" class="btn btn-warning">Restore Original</button>
                    </div>
                </form>
            </div>
        </div>
        <div id="image-browser" class="image-browser" style="display: none;"></div>
    </div>
</template>

<template id="ve-image-browser-template">
    <div class="image-browser-header">
        <h4>Browse Images</h4>
        <button type="button" id="close-browser" class="close-btn" aria-label="Close image browser">×</button>
    </div>
    <div class="image-browser-content">
        <div class="image-browser-toolbar">
            <input type="text" id="image-search" placeholder="Search images..." class="form-control">
            <select id="image-sort" class="form-control">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name</option>
            </select>
        </div>
        <div id="image-grid" class="image-grid">
            <div class="loading-spinner"></div>
        </div>
        <div id="image-pagination" class="image-pagination">
            <button type="button" id="prev-page" class="btn btn-secondary" disabled="">Previous</button>
            <span id="page-info">Page 1</span>
            <button type="button" id="next-page" class="btn btn-secondary">Next</button>
        </div>
    </div>
</template>

 
</body></html>