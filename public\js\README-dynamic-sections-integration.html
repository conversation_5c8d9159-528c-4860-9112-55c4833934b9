<!--
# Dynamic Sections Integration Guide

This file contains HTML snippets for integrating dynamic sections into HTML pages.
This is DEVELOPER DOCUMENTATION - not a standalone HTML page.

## Integration Steps:

1. Add the layout clearance before closing </main>
2. Add the dynamic sections container after </main>
3. Load the dynamic sections script before closing </body>

## HTML Snippets to Copy:
-->

<!-- STEP 1: Add this right before the closing </main> tag -->
<div style="clear: both; width: 100%; height: 50px;"></div>
</main>

<!-- STEP 2: Add this right after the closing </main> tag -->
<!-- Clear separator before dynamic sections -->
<div class="dynamic-sections-separator"></div>

<!-- Dynamic sections will be added here -->
<section id="dynamicSections"></section>

<!-- STEP 3: Add this right before the closing </body> tag -->
<!-- Load the dynamic sections script -->
<script type="module" src="/js/dynamic-sections.js"></script>

<!--
## Notes:
- The dynamic-sections.js script automatically creates containers for top/middle/bottom positioning
- The #dynamicSections container is used for bottom-positioned sections by default
- Additional containers (#dynamicSectionsTop, #dynamicSectionsMiddle) are created automatically
- This integration is already implemented in most HTML files in the public folder
-->
