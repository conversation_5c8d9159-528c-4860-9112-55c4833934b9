# Tutors Alliance Scotland (TAS) Website

> **Mission**: A charity website helping disadvantaged Scottish pupils find cost-effective tutoring that suits their budget and circumstances.

## 🎯 Project Overview

**Tutors Alliance Scotland** is a comprehensive web platform that connects tutors with families across Scotland, with a special focus on supporting disadvantaged children. The website features a sophisticated content management system, tutor directory, blog platform, and dynamic page builder.

### Key Features
- **Tutor Directory**: Searchable database of qualified tutors with regional filtering
- **Dynamic Content Management**: Live website editing with visual editor
- **Blog Platform**: SEO-optimized blog system for educational content
- **Page Builder**: Create custom pages using modular sections
- **Multi-Role Authentication**: Admin, tutor, parent, and blog writer access levels
- **Responsive Design**: Mobile-first approach with Samsung device optimizations

## 🏗️ Architecture Overview

### Technology Stack
- **Frontend**: Vanilla JavaScript, HTML5, CSS3 (modular architecture)
- **Backend**: Node.js serverless functions (Vercel)
- **Database**: MongoDB Atlas with Mongoose ODM
- **Authentication**: JWT with HTTP-only cookies + CSRF protection
- **File Storage**: Vercel Blob + Google Cloud Storage for large files
- **Deployment**: Vercel (12 function limit on free tier)
- **Testing**: Vitest (unit/integration) + Playwright (E2E)

### Deployment Constraints
- **Vercel Free Tier**: Limited to 12 API functions (currently at capacity)
- **No Additional Routes**: Adding new API endpoints incurs charges
- **Serverless Architecture**: All backend logic in `/api` folder
- **Static Assets**: Served from `/public` directory

## 📁 Project Structure

```
tutorScotland/
├── api/                          # Serverless API functions (12 max)
│   ├── addTutor.js              # Tutor CRUD operations
│   ├── blog-writer.js           # Blog management API
│   ├── connectToDatabase.js     # MongoDB connection utility
│   ├── login.js                 # Authentication & JWT handling
│   ├── protected.js             # Auth middleware
│   ├── sections.js              # Dynamic sections & pages API
│   ├── tutors.js                # Tutor search & display
│   ├── upload-image.js          # Image upload to Vercel Blob
│   └── video-sections.js        # Video content management
├── models/                       # MongoDB schemas
│   ├── Blog.js                  # Blog posts with SEO fields
│   ├── Order.js                 # Section ordering for pages
│   ├── Section.js               # Dynamic sections & content overrides
│   ├── Tutor.js                 # Tutor profiles
│   └── User.js                  # User accounts (admin/tutor/parent/blogger)
├── public/                       # Static frontend assets
│   ├── css/                     # Modular CSS architecture
│   │   ├── styles2.css          # Main stylesheet (2971 lines)
│   │   ├── nav.css              # Navigation & team grid styles
│   │   ├── admin-forms.css      # Admin interface styling
│   │   ├── admin-tables.css     # Admin table styling
│   │   ├── animation-module.css # Animations & transitions
│   │   ├── button-module.css    # Button components
│   │   ├── editor.css           # Visual editor styling
│   │   ├── footer-module.css    # Footer component styles
│   │   ├── header-banner.css    # Header & banner styles
│   │   ├── layout-module.css    # Grid & layout systems
│   │   ├── responsive-module.css # Responsive redirects
│   │   ├── typography-module.css # Font & text styles
│   │   └── ui-responsive-module.css # Mobile responsiveness
│   ├── js/                      # Frontend JavaScript modules
│   │   ├── dynamic-sections.js  # Dynamic content loader (1019 lines)
│   │   ├── dynamic-nav.js       # Navigation interactions
│   │   ├── dynamic-page.js      # Dynamic page rendering
│   │   ├── visual-editor-v2.js  # Live content editing
│   │   ├── visual-editor.js     # Legacy visual editor
│   │   ├── responsive-helper.js # Viewport management
│   │   ├── upload-helper.js     # File upload utilities
│   │   ├── nav-loader.js        # Navigation loader
│   │   ├── rolling-banner.js    # Rolling banner animations
│   │   ├── video-player.js      # Video player component
│   │   ├── google-cloud-upload.js # Google Cloud integration
│   │   ├── pages.js             # Page constants
│   │   ├── README-dynamic-sections-integration.html # Integration docs
│   │   └── editor/              # Visual editor components
│   │       ├── api-service.js   # Editor API service
│   │       ├── editor-state.js  # State management
│   │       ├── override-engine.js # Content override system
│   │       ├── ui-manager.js    # UI management
│   │       └── features/        # Editor features
│   │           ├── image-browser.js # Image selection
│   │           └── section-sorter.js # Section reordering
│   ├── images/                  # Static images (50+ files)
│   ├── partials/                # Reusable HTML components
│   │   └── main-nav.html        # Main navigation template
│   ├── admin.html               # Admin dashboard
│   ├── blogWriter.html          # Blog management interface
│   ├── index.html               # Homepage
│   ├── about-us.html            # Team information
│   ├── contact.html             # Contact page
│   ├── parents.html             # Parents information
│   ├── partnerships.html        # Partnership information
│   ├── tutorConnect.html        # Tutor connection form
│   ├── tutorDirectory.html      # Tutor search interface
│   ├── tutorMembership.html     # Tutor membership info
│   ├── tutorszone.html          # Tutor dashboard
│   ├── publicConnect.html       # Public contact form
│   ├── login.html               # Login interface
│   ├── page.html                # Dynamic page template
│   ├── page-template.html       # Page template
│   └── demo-context-indicators.html # Development utility
├── tests/                        # Comprehensive test suite
│   ├── unit/                    # Unit tests (Vitest)
│   ├── integration/             # API integration tests
│   ├── e2e/                     # End-to-end tests (Playwright)
│   └── smoke/                   # CSS preservation tests
├── scripts/                      # Database migration scripts
├── bin/                         # Utility scripts
└── docs/                        # Technical documentation
```

## 🔐 Authentication System

### User Roles
- **Admin**: Full access to all management interfaces
- **Blog Writer**: Access to blog creation and editing
- **Tutor**: Access to tutor zone (limited functionality)
- **Parent**: Basic user access

### Security Features
- JWT tokens with HTTP-only cookies
- CSRF protection
- Role-based access control
- Secure password hashing (bcrypt)
- Admin-only API endpoints

### Login Flow
```javascript
POST /api/login → JWT cookie → Role-based redirect
- Admin → /admin.html
- Blog Writer → /blogWriter.html
- Tutor → /tutorszone.html
```

## 🎛️ Admin Interfaces

### Admin Dashboard (`/admin.html`)
**Access**: Admin role required
**Features**:
- **Content Management**: Create/edit dynamic sections and full pages
- **Tutor Management**: CRUD operations for tutor profiles
- **Page Builder**: Build pages from modular sections (standard, team, list, testimonial, video)
- **Image/Video Upload**: Vercel Blob + Google Cloud integration
- **Section Ordering**: Drag-and-drop reordering with visual editor

### Blog Writer Interface (`/blogWriter.html`)
**Access**: Blog writer role required
**Features**:
- Rich text blog editor with image upload
- SEO metadata fields (title, description, keywords)
- Category and tag management
- Draft/publish workflow
- Reading time calculation

### Visual Editor System
**Access**: Admin users only
**Features**:
- **Live Editing**: Edit content directly on any webpage
- **Content Overrides**: Modify static HTML content dynamically
- **Block-Level Editing**: Edit individual paragraphs, headings, images
- **Persistent Changes**: All edits saved to database and applied site-wide

## 🧩 Dynamic Sections System

### Section Types
1. **Standard**: Heading, text content, optional image and CTA button
2. **Team Members**: Grid layout with member cards (name, bio, quote, image)
3. **List**: Bulleted or numbered lists with optional images
4. **Testimonials**: Quote cards with author attribution and star ratings
5. **Video**: Embedded video players with Google Cloud integration

### Section Placement
- **Top**: Above main content
- **Middle**: Integrated within main content
- **Bottom**: Below main content (default)

### Technical Implementation
```javascript
// Dynamic sections loaded via AJAX
fetch(`/api/sections?page=${currentPage}`)
  .then(sections => renderSections(sections))
  .then(() => initializeVisualEditor())
```

## 🗄️ Database Models

### Core Collections

#### Tutors
```javascript
{
  name: String,
  subjects: [String],           // e.g., ["Mathematics", "English"]
  costRange: String,           // e.g., "__P__" (£ symbols as __P__)
  badges: [String],            // e.g., ["PVG Registered", "Qualified Teacher"]
  regions: [String],           // e.g., ["Highlands", "Online"]
  contact: String,             // Email or website
  imagePath: String,           // Vercel Blob URL
  description: String
}
```

#### Sections (Dynamic Content)
```javascript
{
  page: String,                // Target page (e.g., "index", "about-us")
  heading: String,
  text: String,               // HTML content
  image: String,              // Vercel Blob URL
  layout: String,             // "standard", "team", "list", "testimonial", "video"
  position: String,           // "top", "middle", "bottom"
  isPublished: Boolean,
  isFullPage: Boolean,        // True for standalone pages
  isContentOverride: Boolean, // True for live editing overrides

  // Visual Editor Block IDs
  headingBlockId: String,     // UUID for heading element
  contentBlockId: String,     // UUID for content element
  imageBlockId: String,       // UUID for image element
  buttonBlockId: String,      // UUID for button element

  // Section-specific data
  team: [TeamMemberSchema],   // For team sections
  list: [ListItemSchema],     // For list sections
  testimonials: [TestimonialSchema], // For testimonial sections
  videoUrl: String,           // For video sections

  // Navigation
  showInNav: Boolean,
  navCategory: String,
  slug: String               // URL-friendly identifier
}
```

#### Blogs
```javascript
{
  title: String,
  author: String,
  content: String,            // HTML content
  excerpt: String,
  category: [String],         // ["tutor", "parent"]
  imagePath: String,
  publishDate: Date,

  // SEO Fields
  metaDescription: String,    // Max 160 chars
  slug: String,              // Unique URL identifier
  tags: [String],
  featured: Boolean,
  status: String,            // "draft", "published", "archived"
  focusKeyword: String,
  readingTime: Number        // Auto-calculated
}
```

#### Users
```javascript
{
  name: String,
  email: String,             // Unique
  password: String,          // Bcrypt hashed
  role: String              // "admin", "tutor", "parent", "blogwriter"
}
```

## 🔌 API Endpoints

### Authentication
- `POST /api/login` - User authentication
- `GET /api/login?check=admin` - Admin status verification
- `GET /api/protected?role=admin` - Role-based access control

### Content Management
- `GET /api/sections?page=index` - Get dynamic sections for page
- `POST /api/sections` - Create new section
- `PUT /api/sections?id=:id` - Update existing section
- `DELETE /api/sections?id=:id` - Delete section
- `GET /api/sections?isFullPage=true` - Get standalone pages
- `GET /api/sections?operation=overrides&page=:page` - Get content overrides

### Tutor Management
- `GET /api/tutors?format=json` - Get all tutors (admin)
- `GET /api/tutors` - Public tutor search with filtering
- `POST /api/addTutor` - Create new tutor (admin)
- `PUT /api/addTutor?id=:id` - Update tutor (admin)
- `DELETE /api/addTutor?id=:id` - Delete tutor (admin)

### Blog Management
- `GET /api/blog-writer` - Get all blogs
- `POST /api/blog-writer` - Create/update blog post
- `DELETE /api/blog-writer?id=:id` - Delete blog post
- `GET /api/blog-writer?migrate=true` - Run blog schema migration

### File Upload
- `POST /api/upload-image` - Upload images to Vercel Blob
- `GET /api/video-sections?operation=list-videos` - List available videos
- `POST /api/video-sections` - Create video section

## 🎨 Frontend Architecture

### CSS Organization
- **Modular Approach**: Separate files for different concerns
- **Mobile-First**: Responsive design with Samsung device optimizations
- **Component-Based**: Reusable UI components and utilities

### JavaScript Modules
- **ES6 Modules**: Modern import/export syntax
- **Dynamic Loading**: Sections loaded via AJAX
- **Event-Driven**: Pub/sub pattern for component communication

### Key Frontend Files
- `dynamic-sections.js` - Core dynamic content system (1019 lines)
- `visual-editor-v2.js` - Live editing interface
- `responsive-helper.js` - Viewport management and mobile fixes
- `upload-helper.js` - File upload utilities

## 🧪 Testing Strategy

### Test Types
- **Unit Tests**: Model validation, utility functions (Vitest)
- **Integration Tests**: API endpoints, database operations (Vitest + Supertest)
- **E2E Tests**: User workflows, cross-browser compatibility (Playwright)
- **Visual Regression**: CSS preservation, responsive design (Playwright)

### Test Configuration
```bash
npm run test:unit          # Unit tests only
npm run test:integration   # API integration tests
npm run test:e2e          # End-to-end tests
npm run test:coverage     # Coverage report
npm run test:all          # Full test suite
```

### Browser Testing Matrix
- Desktop: Chrome, Firefox, Safari
- Mobile: Chrome Mobile, Safari Mobile, Samsung Galaxy
- Specific Samsung viewport testing for known issues

## 🚀 Development Setup

### Prerequisites
- Node.js 18+ (pinned for Sharp image processing compatibility)
- MongoDB Atlas account
- Vercel account (for deployment)
- Google Cloud Storage account (for large file uploads)

### Environment Variables
Create `.env.local` file:
```bash
# Database
MONGODB_USERNAME=your_username
MONGODB_PASSWORD=your_password

# Authentication
JWT_SECRET=your_jwt_secret_key

# File Storage
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token

# Google Cloud (optional, for large video uploads)
GOOGLE_CLOUD_PROJECT_ID=your_project_id
GOOGLE_CLOUD_PRIVATE_KEY=your_private_key
GOOGLE_CLOUD_CLIENT_EMAIL=your_service_account_email
```

### Installation & Setup
```bash
# Clone repository
git clone https://github.com/tobyStone/tutorScotland.git
cd tutorScotland

# Install dependencies
npm install

# Start development server
npm run start

# Run tests
npm run test:all
```

### Database Setup
1. Create MongoDB Atlas cluster
2. Set up database user with read/write permissions
3. Configure IP whitelist for your development environment
4. Database and collections will be created automatically

## 📋 Deployment Guide

### Vercel Deployment
1. Connect GitHub repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Important Deployment Notes
- **Function Limit**: Currently using 12/12 Vercel functions (at free tier limit)
- **Runtime**: Pinned to `nodejs18.x` for Sharp compatibility
- **Build Settings**: No build step required (static + serverless)

## 🔧 Maintenance & Operations

### Regular Maintenance Tasks

#### Security Updates
```bash
# Check for vulnerabilities
npm audit

# Fix automatically fixable issues
npm audit fix

# For breaking changes (test first)
npm audit fix --force
```

#### Database Migrations
- Use scripts in `/bin` folder for schema updates
- Always backup before running migrations
- Test migrations in development first

#### Content Backup
- Dynamic sections stored in MongoDB
- Static content in HTML files
- Images in Vercel Blob storage
- Regular database backups recommended

### Monitoring & Debugging
- **Error Tracking**: Console logging in serverless functions
- **Performance**: Vercel Analytics dashboard
- **Database**: MongoDB Atlas monitoring
- **File Storage**: Vercel Blob usage tracking

## 🐛 Known Issues & Workarounds

### Samsung Mobile Display Issues
- **Problem**: Team member grids collapse on Samsung devices
- **Solution**: Aggressive CSS media queries + JavaScript fallback
- **Files**: `styles2.css` (lines 2952-3002), `responsive-helper.js`

### Vercel Function Limits
- **Problem**: Free tier limited to 12 API functions
- **Workaround**: Consolidated endpoints, avoided API route expansion
- **Impact**: New features must reuse existing endpoints

### Image Upload Constraints
- **Small files** (<4.5MB): Vercel Blob via serverless function
- **Large files** (>4.5MB): Direct Google Cloud upload
- **Fallback**: Manual upload guidance for users

## 📚 Key Documentation

### Internal Documentation
- `/docs/css-technical-debt.md` - CSS architecture notes
- `/docs/google-cloud-setup-guide.md` - File upload configuration
- `/VISUAL_EDITOR_PERSISTENCE_SOLUTION.md` - Visual editor architecture
- `/tests/README.md` - Testing strategy and guidelines

### Code Comments
- Extensive JSDoc comments in JavaScript modules
- Inline CSS comments explaining complex responsive fixes
- API endpoint documentation in function headers

## 🤝 Contributing Guidelines

### Code Style
- **JavaScript**: ES6+ modules, async/await preferred
- **CSS**: Mobile-first, modular architecture
- **HTML**: Semantic markup with accessibility considerations

### Development Workflow
1. Create feature branch from `main`
2. Write tests for new functionality
3. Ensure all tests pass (`npm run test:all`)
4. Test on multiple devices/browsers
5. Create pull request with detailed description

### Testing Requirements
- Unit tests for new utility functions
- Integration tests for API changes
- E2E tests for user-facing features
- Visual regression tests for CSS changes

---

## 📞 Support & Contact

**Project Owner**: Toby Stone
**Organization**: Tutors Alliance Scotland
**Purpose**: Supporting disadvantaged Scottish pupils through accessible tutoring
