import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';

let mongoServer;

export async function setupTestDB() {
    try {
        // Check if we're in CI with a MongoDB service available
        const isCI = process.env.CI === 'true' || process.env.GITHUB_ACTIONS === 'true';
        const mongoUri = process.env.MONGODB_URI;

        if (isCI && mongoUri) {
            console.log('CI environment detected, using provided MongoDB service...');
            console.log('MongoDB URI:', mongoUri);

            // Close any existing connections
            if (mongoose.connection.readyState !== 0) {
                console.log('Closing existing MongoDB connection...');
                await mongoose.disconnect();
            }

            console.log('Connecting to CI test database...');
            await mongoose.connect(mongoUri, {
                serverSelectionTimeoutMS: 10000,
                connectTimeoutMS: 10000,
            });
            console.log('CI test database connected successfully');
        } else {
            console.log('Local environment detected, starting MongoDB Memory Server...');
            mongoServer = await MongoMemoryServer.create({
                instance: {
                    dbName: 'tutorscotland-test'
                },
                binary: {
                    downloadDir: './node_modules/.cache/mongodb-memory-server',
                    version: '6.0.4', // Use a smaller, faster version
                    skipMD5: true // Skip MD5 verification for faster startup
                }
            });
            const memoryMongoUri = mongoServer.getUri();
            console.log('MongoDB Memory Server started at:', memoryMongoUri);

            // Close any existing connections
            if (mongoose.connection.readyState !== 0) {
                console.log('Closing existing MongoDB connection...');
                await mongoose.disconnect();
            }

            console.log('Connecting to local test database...');
            await mongoose.connect(memoryMongoUri, {
                serverSelectionTimeoutMS: 10000, // 10 second timeout
                connectTimeoutMS: 10000,
            });
            console.log('Local test database connected successfully');
        }
    } catch (error) {
        console.error('Failed to setup test database:', error);
        // Clean up on failure
        if (mongoServer) {
            try {
                await mongoServer.stop();
            } catch (cleanupError) {
                console.error('Error cleaning up MongoDB server:', cleanupError);
            }
        }
        throw error;
    }
}

export async function teardownTestDB() {
    try {
        console.log('Tearing down test database...');

        if (mongoose.connection.readyState !== 0) {
            console.log('Disconnecting from MongoDB...');
            await mongoose.disconnect();
        }

        // Only stop MongoDB Memory Server if we started one (local environment)
        if (mongoServer) {
            console.log('Stopping MongoDB Memory Server...');
            await mongoServer.stop();
            mongoServer = null;
        } else {
            console.log('Using external MongoDB service, no memory server to stop');
        }
        console.log('Test database torn down successfully');
    } catch (error) {
        console.error('Failed to teardown test database:', error);
        // Don't throw in teardown to avoid masking test failures
        console.error('Continuing despite teardown error...');
    }
}

export async function clearTestDB() {
    try {
        if (mongoose.connection.readyState === 0) {
            throw new Error('Database not connected');
        }
        
        const collections = mongoose.connection.collections;
        const clearPromises = Object.keys(collections).map(key => 
            collections[key].deleteMany({})
        );
        
        await Promise.all(clearPromises);
        console.log('Test database cleared successfully');
    } catch (error) {
        console.error('Failed to clear test database:', error);
        throw error;
    }
}

// Helper function to create test data
export async function seedTestData() {
    let User;
    try {
        User = mongoose.model('User');
    } catch {
        User = require('../../models/user.js');
    }
    const bcrypt = require('bcryptjs');
    
    // Create test admin user
    const adminUser = await User.create({
        name: 'Test Admin',
        email: '<EMAIL>',
        password: await bcrypt.hash('testpassword123', 10),
        role: 'admin'
    });
    
    // Create test parent user
    const parentUser = await User.create({
        name: 'Test Parent',
        email: '<EMAIL>',
        password: await bcrypt.hash('testpassword123', 10),
        role: 'parent'
    });
    
    return { adminUser, parentUser };
}
